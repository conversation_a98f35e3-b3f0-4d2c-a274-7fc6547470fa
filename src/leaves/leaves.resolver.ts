import {
  Resolver,
  Query,
  Mutation,
  Args,
  Parent,
  Context,
  ResolveField,
} from '@nestjs/graphql';
import { LeavesService } from './leaves.service';
import { Leave } from './entities/leave.entity';
import { User } from '../users/entities/user.entity';
import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { FindLeavesInput } from './dto/find-leaves.input';
import { GqlContext } from 'src/app.module';
import _ from 'lodash';

@Resolver(() => Leave)
export class LeavesResolver {
  constructor(private readonly leavesService: LeavesService) {}

  @ResolveField(() => User, { name: 'user', nullable: true })
  getUsers(@Parent() leave: Leave, @Context() context: GqlContext) {
    return context.loaders.usersLoader.load(leave.user);
  }

  @ResolveField(() => User, { name: 'approvedBy', nullable: true })
  getApprovedBy(@Parent() leave: Leave, @Context() context: GqlContext) {
    return leave.approvedBy
      ? context.loaders.usersLoader.load(leave.approvedBy)
      : null;
  }

  @Mutation(() => Leave)
  createLeave(@Args('createLeaveInput') CreateLeaveInput: CreateLeaveInput) {
    return this.leavesService.create(CreateLeaveInput);
  }

  @Query(() => [Leave], { name: 'leaves' })
  findAll(
    @Args('filter', { type: () => FindLeavesInput, nullable: true })
    filter?: FindLeavesInput,
  ) {
    console.log(filter);
    return this.leavesService.findAll({ ..._ });
  }

  @Query(() => Leave, { name: 'leave' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.leavesService.findOne({ _id: id });
  }

  @Mutation(() => Leave)
  updateLeave(
    @Args('id') id: string,
    @Args('updateLeaveInput') updateLeaveInput: UpdateLeaveInput,
  ) {
    return this.leavesService.update(id, updateLeaveInput);
  }
}
